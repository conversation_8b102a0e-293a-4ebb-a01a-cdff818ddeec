#!/usr/bin/env python3
# Copyright 2024 ByteDance and/or its affiliates.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Integration Script for Tensor Parallelism in Protenix Model

This script demonstrates how to integrate the new tensor parallelism implementation
with the existing Protenix model and data parallelism.

Usage:
    # Single GPU inference (baseline)
    python integrate_tensor_parallel.py --mode baseline --config_path configs/inference.yaml

    # Data parallelism only (existing functionality)
    torchrun --nproc_per_node=4 integrate_tensor_parallel.py \
        --mode data_parallel --config_path configs/inference.yaml

    # Tensor parallelism for intermediate modules
    torchrun --nproc_per_node=4 integrate_tensor_parallel.py \
        --mode tensor_parallel --config_path configs/inference.yaml

    # Hybrid: Data + Tensor parallelism
    torchrun --nproc_per_node=8 integrate_tensor_parallel.py \
        --mode hybrid --config_path configs/inference.yaml \
        --tp_size 2 --dp_size 4
"""

import argparse
import os
import sys
import time
import torch
import torch.distributed as dist
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from parallelize_protenix import (
    parallelize_protenix,
    create_protenix_parallel_config,
    ProtenixJobConfig
)
from protenix.model.tensor_parallel_config import create_default_tp_config
from protenix.utils.logger import get_logger
from protenix.utils.distributed import distributed_available

logger = get_logger(__name__)


def setup_distributed():
    """Setup distributed training environment."""
    if not dist.is_available():
        logger.error("Distributed training not available")
        return False
    
    if not dist.is_initialized():
        try:
            dist.init_process_group(backend="nccl", timeout=torch.distributed.default_pg_timeout)
        except Exception as e:
            logger.error(f"Failed to initialize distributed: {e}")
            return False
    
    rank = dist.get_rank()
    world_size = dist.get_world_size()
    local_rank = int(os.environ.get("LOCAL_RANK", 0))
    
    torch.cuda.set_device(local_rank)
    
    logger.info(f"Distributed setup: rank={rank}, world_size={world_size}, local_rank={local_rank}")
    return True


def load_protenix_model(config_path: str, device: torch.device):
    """Load Protenix model from configuration."""
    try:
        # This is a placeholder - replace with actual model loading logic
        from protenix.model.protenix import Protenix
        from protenix.configs import load_config
        
        # Load configuration
        configs = load_config(config_path)
        
        # Create model
        model = Protenix(configs)
        model = model.to(device)
        
        logger.info(f"Loaded Protenix model with {sum(p.numel() for p in model.parameters())} parameters")
        return model, configs
        
    except ImportError:
        logger.warning("Could not import Protenix model, using dummy model for demonstration")
        return create_dummy_protenix_model(device), None


def create_dummy_protenix_model(device: torch.device):
    """Create a dummy Protenix model for testing when real model is not available."""
    
    class DummyProtenix(torch.nn.Module):
        def __init__(self):
            super().__init__()
            
            # Intermediate linear layers (main targets for tensor parallelism)
            self.linear_no_bias_sinit = torch.nn.Linear(449, 384, bias=False)
            self.linear_no_bias_zinit1 = torch.nn.Linear(384, 128, bias=False)
            self.linear_no_bias_zinit2 = torch.nn.Linear(384, 128, bias=False)
            self.linear_no_bias_token_bond = torch.nn.Linear(1, 128, bias=False)
            self.linear_no_bias_z_cycle = torch.nn.Linear(128, 128, bias=False)
            self.linear_no_bias_s = torch.nn.Linear(384, 384, bias=False)
            
            # Heads (targets for tensor parallelism)
            self.distogram_head = DummyDistogramHead()
            self.confidence_head = DummyConfidenceHead()
            
            # Major components (targets for data parallelism)
            self.pairformer_stack = DummyPairformerStack()
            self.diffusion_module = DummyDiffusionModule()
            
            # Hybrid parallel wrapper
            from protenix.model.hybrid_parallel import HybridParallelWrapper
            self.hybrid_parallel = HybridParallelWrapper(self, enable_hybrid_parallel=False)
        
        def get_pairformer_output(self, input_feature_dict, N_cycle, inplace_safe=True, chunk_size=None):
            batch_size = input_feature_dict["dummy"].shape[0]
            seq_len = input_feature_dict["dummy"].shape[1]
            device = input_feature_dict["dummy"].device
            
            s_inputs = torch.randn(batch_size, seq_len, 449, device=device)
            s_trunk = torch.randn(batch_size, seq_len, 384, device=device)
            z_trunk = torch.randn(batch_size, seq_len, seq_len, 128, device=device)
            return s_inputs, s_trunk, z_trunk
        
        def sample_diffusion(self, denoise_net, input_feature_dict, s_inputs, s_trunk, z_trunk, N_sample, noise_schedule, inplace_safe=True):
            batch_size = s_inputs.shape[0]
            n_atoms = 100
            device = s_inputs.device
            return torch.randn(batch_size, N_sample, n_atoms, 3, device=device)
        
        def forward(self, input_feature_dict):
            # Dummy forward pass demonstrating data flow through intermediate modules
            x = input_feature_dict["dummy"]
            
            # Intermediate linear transformations (tensor parallel targets)
            s_init = self.linear_no_bias_sinit(x)
            z_init1 = self.linear_no_bias_zinit1(s_init)
            z_init2 = self.linear_no_bias_zinit2(s_init)
            
            # Simulate pairformer processing
            s_trunk, z_trunk = self.pairformer_stack(s_init, z_init1 + z_init2)
            
            # Apply heads (tensor parallel targets)
            distogram_logits = self.distogram_head(z_trunk)
            confidence_outputs = self.confidence_head(x, s_init, s_trunk, z_trunk)
            
            return {
                "distogram": distogram_logits,
                "confidence": confidence_outputs,
                "s_trunk": s_trunk,
                "z_trunk": z_trunk
            }
    
    class DummyDistogramHead(torch.nn.Module):
        def __init__(self):
            super().__init__()
            self.linear = torch.nn.Linear(128, 64)
        
        def forward(self, z):
            return self.linear(z)
    
    class DummyConfidenceHead(torch.nn.Module):
        def __init__(self):
            super().__init__()
            self.linear_no_bias_s1 = torch.nn.Linear(449, 128, bias=False)
            self.linear_no_bias_s2 = torch.nn.Linear(449, 128, bias=False)
            self.linear_no_bias_d = torch.nn.Linear(39, 128, bias=False)
            self.linear_no_bias_pae = torch.nn.Linear(128, 64, bias=False)
            self.linear_no_bias_pde = torch.nn.Linear(128, 64, bias=False)
        
        def forward(self, s_inputs, s_init, s_trunk, z_trunk):
            return {
                "pae": self.linear_no_bias_pae(z_trunk),
                "pde": self.linear_no_bias_pde(z_trunk)
            }
    
    class DummyPairformerStack(torch.nn.Module):
        def forward(self, s, z):
            return s, z
    
    class DummyDiffusionModule(torch.nn.Module):
        def forward(self, *args, **kwargs):
            return torch.randn(1, 10, 100, 3, device=next(self.parameters()).device)
    
    return DummyProtenix().to(device)


def run_baseline_inference(model, device):
    """Run baseline inference without any parallelism."""
    logger.info("Running baseline inference (single GPU, no parallelism)")
    
    # Create dummy input
    batch_size = 2
    seq_len = 64
    dummy_input = {
        "dummy": torch.randn(batch_size, seq_len, 449, device=device)
    }
    
    # Time the forward pass
    start_time = time.time()
    with torch.no_grad():
        output = model(dummy_input)
    end_time = time.time()
    
    logger.info(f"Baseline inference completed in {end_time - start_time:.3f}s")
    return output


def run_data_parallel_inference(model, device, world_size):
    """Run inference with data parallelism only."""
    logger.info("Running data parallel inference")
    
    # Create parallel configuration for data parallelism only
    parallel_dims, world_mesh = create_protenix_parallel_config(
        world_size=world_size,
        enable_tp=False,
        enable_dp=True,
        enable_hybrid_dp=True
    )
    
    job_config = ProtenixJobConfig(
        enable_compile=False,
        enable_activation_checkpointing=False
    )
    
    # Apply parallelization
    model = parallelize_protenix(
        model=model,
        world_mesh=world_mesh,
        parallel_dims=parallel_dims,
        job_config=job_config
    )
    
    logger.info("Data parallelism applied successfully")
    return model


def run_tensor_parallel_inference(model, device, world_size):
    """Run inference with tensor parallelism only."""
    logger.info("Running tensor parallel inference")
    
    # Create parallel configuration for tensor parallelism only
    parallel_dims, world_mesh = create_protenix_parallel_config(
        world_size=world_size,
        enable_tp=True,
        enable_dp=False,
        tp_size=world_size
    )
    
    job_config = ProtenixJobConfig(
        enable_compile=False,
        enable_activation_checkpointing=False,
        tp_strategy="balanced"
    )
    
    # Apply parallelization
    model = parallelize_protenix(
        model=model,
        world_mesh=world_mesh,
        parallel_dims=parallel_dims,
        job_config=job_config
    )
    
    logger.info("Tensor parallelism applied successfully")
    return model


def run_hybrid_parallel_inference(model, device, world_size, tp_size, dp_size):
    """Run inference with both data and tensor parallelism."""
    logger.info(f"Running hybrid parallel inference (TP={tp_size}, DP={dp_size})")
    
    if tp_size * dp_size != world_size:
        raise ValueError(f"tp_size ({tp_size}) * dp_size ({dp_size}) != world_size ({world_size})")
    
    # Create parallel configuration for hybrid parallelism
    parallel_dims, world_mesh = create_protenix_parallel_config(
        world_size=world_size,
        enable_tp=True,
        enable_dp=True,
        enable_hybrid_dp=True,
        tp_size=tp_size,
        dp_size=dp_size
    )
    
    job_config = ProtenixJobConfig(
        enable_compile=False,
        enable_activation_checkpointing=True,
        tp_strategy="balanced"
    )
    
    # Apply parallelization
    model = parallelize_protenix(
        model=model,
        world_mesh=world_mesh,
        parallel_dims=parallel_dims,
        job_config=job_config
    )
    
    logger.info(f"Hybrid parallelism applied successfully (TP={tp_size}, DP={dp_size})")
    return model


def main():
    parser = argparse.ArgumentParser(description="Tensor Parallelism Integration for Protenix")
    parser.add_argument(
        "--mode",
        choices=["baseline", "data_parallel", "tensor_parallel", "hybrid"],
        default="baseline",
        help="Parallelization mode"
    )
    parser.add_argument(
        "--config_path",
        type=str,
        default="configs/inference.yaml",
        help="Path to model configuration file"
    )
    parser.add_argument("--tp_size", type=int, default=2, help="Tensor parallelism size")
    parser.add_argument("--dp_size", type=int, default=2, help="Data parallelism size")
    
    args = parser.parse_args()
    
    # Setup device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Setup distributed if needed
    is_distributed = args.mode != "baseline"
    if is_distributed and not setup_distributed():
        logger.error("Failed to setup distributed environment")
        return
    
    world_size = dist.get_world_size() if is_distributed else 1
    rank = dist.get_rank() if is_distributed else 0
    
    try:
        # Load model
        logger.info(f"Loading Protenix model on rank {rank}")
        model, configs = load_protenix_model(args.config_path, device)
        
        # Apply parallelization based on mode
        if args.mode == "baseline":
            output = run_baseline_inference(model, device)
        elif args.mode == "data_parallel":
            model = run_data_parallel_inference(model, device, world_size)
        elif args.mode == "tensor_parallel":
            model = run_tensor_parallel_inference(model, device, world_size)
        elif args.mode == "hybrid":
            model = run_hybrid_parallel_inference(model, device, world_size, args.tp_size, args.dp_size)
        
        logger.info(f"Integration completed successfully in {args.mode} mode")
        
    except Exception as e:
        logger.error(f"Integration failed: {e}")
        raise
    
    finally:
        if is_distributed and dist.is_initialized():
            dist.destroy_process_group()


if __name__ == "__main__":
    main()
