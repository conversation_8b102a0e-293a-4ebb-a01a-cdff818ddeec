#!/usr/bin/env python3
# Copyright 2024 ByteDance and/or its affiliates.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Tensor Parallelism Performance Diagnosis Script

This script helps diagnose why DP+TP might not show performance improvements
over pure DP by analyzing timing data and model configuration.
"""

import json
import os
import sys
import argparse
from pathlib import Path
import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, List, Any

def load_timing_data(timing_file: str) -> Dict[str, Any]:
    """Load timing data from JSON file."""
    try:
        with open(timing_file, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading timing data from {timing_file}: {e}")
        return {}

def analyze_timing_breakdown(timing_data: Dict[str, Any]) -> None:
    """Analyze timing breakdown to identify bottlenecks."""
    if not timing_data or 'samples' not in timing_data:
        print("No valid timing data found")
        return
    
    print("\n" + "="*60)
    print("TIMING BREAKDOWN ANALYSIS")
    print("="*60)
    
    for sample in timing_data['samples']:
        sample_name = sample.get('sample_name', 'Unknown')
        timing = sample.get('timing', {})
        complex_info = sample.get('complex_info', {})
        rank = sample.get('processed_by_rank', 'Unknown')
        
        print(f"\nSample: {sample_name} (Rank {rank})")
        print(f"Complex Info: N_token={complex_info.get('N_token', 'N/A')}, "
              f"N_atom={complex_info.get('N_atom', 'N/A')}")
        
        # Key timing components
        key_components = [
            'input_embedder',
            'pairformer_stack', 
            'confidence_head',
            'distogram_head',
            'diffusion_module',
            'model_forward_total',
            'total_inference'
        ]
        
        print("\nTiming Breakdown:")
        total_time = timing.get('total_inference', 0)
        for component in key_components:
            if component in timing:
                time_val = timing[component]
                percentage = (time_val / total_time * 100) if total_time > 0 else 0
                print(f"  {component:20s}: {time_val:8.3f}s ({percentage:5.1f}%)")
        
        # Check for tensor parallelism indicators
        print("\nTensor Parallelism Analysis:")
        intermediate_components = [
            'linear_no_bias_sinit',
            'linear_no_bias_zinit1', 
            'linear_no_bias_zinit2',
            'confidence_head',
            'distogram_head'
        ]
        
        intermediate_total = sum(timing.get(comp, 0) for comp in intermediate_components)
        if intermediate_total > 0:
            print(f"  Intermediate modules total: {intermediate_total:.3f}s ({intermediate_total/total_time*100:.1f}%)")
            print(f"  Potential TP speedup target: {intermediate_total:.3f}s")
        else:
            print("  Warning: No intermediate module timing found - TP may not be applied")

def compare_dp_vs_hybrid_performance(dp_timing_dir: str, hybrid_timing_dir: str) -> None:
    """Compare performance between DP-only and DP+TP runs."""
    print("\n" + "="*60)
    print("DP vs DP+TP PERFORMANCE COMPARISON")
    print("="*60)
    
    # Load timing data
    dp_file = os.path.join(dp_timing_dir, "timing_summary.json")
    hybrid_file = os.path.join(hybrid_timing_dir, "timing_summary.json")
    
    dp_data = load_timing_data(dp_file)
    hybrid_data = load_timing_data(hybrid_file)
    
    if not dp_data or not hybrid_data:
        print("Could not load timing data for comparison")
        return
    
    # Compare samples
    dp_samples = {s['sample_name']: s for s in dp_data.get('samples', [])}
    hybrid_samples = {s['sample_name']: s for s in hybrid_data.get('samples', [])}
    
    common_samples = set(dp_samples.keys()) & set(hybrid_samples.keys())
    
    if not common_samples:
        print("No common samples found for comparison")
        return
    
    print(f"Comparing {len(common_samples)} common samples:")
    
    improvements = []
    for sample_name in common_samples:
        dp_sample = dp_samples[sample_name]
        hybrid_sample = hybrid_samples[sample_name]
        
        dp_time = dp_sample['timing'].get('total_inference', 0)
        hybrid_time = hybrid_sample['timing'].get('total_inference', 0)
        
        if dp_time > 0 and hybrid_time > 0:
            speedup = dp_time / hybrid_time
            improvement = (dp_time - hybrid_time) / dp_time * 100
            improvements.append(improvement)
            
            print(f"\n{sample_name}:")
            print(f"  DP time:     {dp_time:.3f}s")
            print(f"  DP+TP time:  {hybrid_time:.3f}s")
            print(f"  Speedup:     {speedup:.2f}x")
            print(f"  Improvement: {improvement:+.1f}%")
            
            # Analyze component-wise differences
            analyze_component_differences(dp_sample['timing'], hybrid_sample['timing'])
    
    if improvements:
        avg_improvement = np.mean(improvements)
        print(f"\nOverall Results:")
        print(f"  Average improvement: {avg_improvement:+.1f}%")
        print(f"  Best improvement:    {max(improvements):+.1f}%")
        print(f"  Worst improvement:   {min(improvements):+.1f}%")
        
        if avg_improvement < 5:
            print("\n⚠️  WARNING: Minimal performance improvement detected!")
            print("   Possible reasons:")
            print("   1. Tensor parallelism not properly applied")
            print("   2. Communication overhead exceeds computation savings")
            print("   3. Intermediate modules are not the bottleneck")
            print("   4. Model size too small to benefit from TP")

def analyze_component_differences(dp_timing: Dict, hybrid_timing: Dict) -> None:
    """Analyze differences in component timing between DP and DP+TP."""
    components = ['pairformer_stack', 'confidence_head', 'distogram_head', 'diffusion_module']
    
    print("  Component differences:")
    for component in components:
        dp_time = dp_timing.get(component, 0)
        hybrid_time = hybrid_timing.get(component, 0)
        
        if dp_time > 0 and hybrid_time > 0:
            diff = (dp_time - hybrid_time) / dp_time * 100
            print(f"    {component:20s}: {diff:+6.1f}%")

def check_tensor_parallel_application(timing_data: Dict[str, Any]) -> None:
    """Check if tensor parallelism was actually applied."""
    print("\n" + "="*60)
    print("TENSOR PARALLELISM APPLICATION CHECK")
    print("="*60)
    
    if not timing_data or 'samples' not in timing_data:
        print("No timing data available")
        return
    
    # Look for indicators that TP was applied
    tp_indicators = []
    
    for sample in timing_data['samples']:
        timing = sample.get('timing', {})
        
        # Check for intermediate module timing
        intermediate_modules = [
            'linear_no_bias_sinit',
            'linear_no_bias_zinit1',
            'linear_no_bias_zinit2', 
            'linear_no_bias_token_bond',
            'linear_no_bias_z_cycle',
            'linear_no_bias_s'
        ]
        
        found_intermediate = any(module in timing for module in intermediate_modules)
        tp_indicators.append(found_intermediate)
    
    if any(tp_indicators):
        print("✅ Tensor parallelism appears to be applied (intermediate module timing found)")
    else:
        print("❌ Tensor parallelism may NOT be applied (no intermediate module timing found)")
        print("\nTroubleshooting steps:")
        print("1. Check if --enable_tensor_parallel flag is set")
        print("2. Verify tensor parallelism imports are working")
        print("3. Check for error messages in logs")
        print("4. Ensure model has the required intermediate modules")

def generate_performance_report(timing_dir: str, output_file: str = None) -> None:
    """Generate a comprehensive performance report."""
    timing_file = os.path.join(timing_dir, "timing_summary.json")
    timing_data = load_timing_data(timing_file)
    
    if output_file is None:
        output_file = os.path.join(timing_dir, "performance_report.txt")
    
    with open(output_file, 'w') as f:
        # Redirect print to file
        original_stdout = sys.stdout
        sys.stdout = f
        
        try:
            print("TENSOR PARALLELISM PERFORMANCE REPORT")
            print("=" * 50)
            print(f"Generated from: {timing_file}")
            print(f"Report time: {os.path.getctime(timing_file)}")
            
            analyze_timing_breakdown(timing_data)
            check_tensor_parallel_application(timing_data)
            
        finally:
            sys.stdout = original_stdout
    
    print(f"Performance report saved to: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Diagnose tensor parallelism performance")
    parser.add_argument("--timing_dir", type=str, required=True,
                       help="Directory containing timing_summary.json")
    parser.add_argument("--compare_with", type=str,
                       help="Directory with DP-only timing data for comparison")
    parser.add_argument("--output_report", type=str,
                       help="Output file for performance report")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.timing_dir):
        print(f"Error: Timing directory {args.timing_dir} does not exist")
        return
    
    timing_file = os.path.join(args.timing_dir, "timing_summary.json")
    if not os.path.exists(timing_file):
        print(f"Error: Timing file {timing_file} does not exist")
        return
    
    # Load and analyze timing data
    timing_data = load_timing_data(timing_file)
    
    # Perform analysis
    analyze_timing_breakdown(timing_data)
    check_tensor_parallel_application(timing_data)
    
    # Compare with DP-only if provided
    if args.compare_with:
        compare_dp_vs_hybrid_performance(args.compare_with, args.timing_dir)
    
    # Generate report
    generate_performance_report(args.timing_dir, args.output_report)
    
    print("\n" + "="*60)
    print("RECOMMENDATIONS")
    print("="*60)
    print("1. Check the log output for 'Tensor parallelism applied successfully'")
    print("2. Verify intermediate module timing is being captured")
    print("3. Compare component-wise timing between DP and DP+TP runs")
    print("4. Consider profiling with different tp_size values (2, 4, 8)")
    print("5. Test with larger models where TP benefits are more pronounced")

if __name__ == "__main__":
    main()
