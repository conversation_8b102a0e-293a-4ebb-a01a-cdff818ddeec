# Tensor Parallelism for Protenix Model

This implementation adds tensor parallelism to intermediate modules in the Protenix model, complementing the existing data parallelism for pairformer and diffusion modules.

## Overview

The tensor parallelism implementation targets the computational bottlenecks between the pairformer and diffusion modules:

1. **Intermediate Linear Layers**: `linear_no_bias_sinit`, `linear_no_bias_zinit1/2`, `linear_no_bias_token_bond`, etc.
2. **Distogram Head**: Linear layer for distance prediction
3. **Confidence Head**: Multiple linear layers and internal pairformer stack for confidence prediction

## Architecture

### Hybrid Parallelism Strategy

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Pairformer    │    │  Intermediate    │    │   Diffusion     │
│                 │    │    Modules       │    │                 │
│ Data Parallel   │───▶│ Tensor Parallel  │───▶│ Data Parallel   │
│ (Run once,      │    │ (Distributed     │    │ (<PERSON><PERSON>l       │
│  broadcast)     │    │  computation)    │    │  sampling)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Key Components

1. **TensorParallelConfig**: Configuration system for enabling/disabling TP on different modules
2. **ProtenixTensorParallelizer**: Main class applying TP to model components
3. **HybridParallelismCoordinator**: Coordinates both data and tensor parallelism
4. **parallelize_protenix()**: Main function following `parallelize_llama.py` pattern

## Usage

### Basic Usage

```python
from parallelize_protenix import parallelize_protenix, create_protenix_parallel_config, ProtenixJobConfig

# Create parallel configuration
parallel_dims, world_mesh = create_protenix_parallel_config(
    world_size=4,
    enable_tp=True,
    enable_dp=True,
    enable_hybrid_dp=True,
    tp_size=2,  # 2-way tensor parallelism
    dp_size=2   # 2-way data parallelism
)

# Create job configuration
job_config = ProtenixJobConfig(
    enable_compile=False,
    enable_activation_checkpointing=True,
    tp_strategy="balanced"
)

# Apply parallelization
model = parallelize_protenix(
    model=model,
    world_mesh=world_mesh,
    parallel_dims=parallel_dims,
    job_config=job_config
)
```

### Configuration Options

#### Tensor Parallelism Strategies

- **`balanced`** (default): Moderate parallelization, good performance/memory trade-off
- **`aggressive`**: Maximum parallelization, highest performance but more memory overhead
- **`conservative`**: Minimal parallelization, lowest memory overhead

#### Module-Specific Configuration

```python
from protenix.model.tensor_parallel_config import TensorParallelConfig

config = TensorParallelConfig(
    enable_tensor_parallel=True,
    tp_size=4,
    
    # Module-specific settings
    parallelize_distogram_head=True,
    parallelize_confidence_head=True,
    parallelize_intermediate_linears=True,
    
    # Confidence head specific
    parallelize_confidence_pairformer=False,  # Disable for memory savings
    confidence_head_tp_strategy="linear_only",
    
    # Performance settings
    use_sequence_parallel=True,
    enable_activation_checkpointing=False
)
```

### Command Line Usage

```bash
# Tensor parallelism only (4 GPUs)
torchrun --nproc_per_node=4 your_script.py --enable_tensor_parallel --tp_size=4

# Hybrid: 2-way TP + 2-way DP (4 GPUs total)
torchrun --nproc_per_node=4 your_script.py \
    --enable_tensor_parallel --tp_size=2 \
    --enable_data_parallel --dp_size=2 \
    --enable_hybrid_dp

# Conservative strategy for memory-constrained environments
torchrun --nproc_per_node=4 your_script.py \
    --enable_tensor_parallel --tp_strategy=conservative
```

## Performance Benefits

### Theoretical Analysis

For a model with:
- Pairformer time: T_pair
- Intermediate modules time: T_intermediate  
- Diffusion time: T_diffusion

**Without Tensor Parallelism:**
```
Total time = T_pair + T_intermediate + T_diffusion
```

**With Tensor Parallelism (TP_size = N):**
```
Total time = T_pair + T_intermediate/N + T_diffusion
Speedup ≈ T_intermediate * (N-1) / N
```

### Expected Improvements

- **Memory Efficiency**: Reduced memory usage for intermediate computations
- **Computational Speedup**: 1.5-3x speedup for intermediate modules depending on TP size
- **Scalability**: Better utilization of multiple GPUs
- **Maintained Accuracy**: Numerically equivalent results to single-GPU execution

## Implementation Details

### Tensor Parallelism Patterns

1. **ColwiseParallel**: Split weight matrices column-wise
   - Used for: Most linear layers, distogram head
   - Pattern: `input @ weight_shard → partial_output`

2. **RowwiseParallel**: Split weight matrices row-wise  
   - Used for: Output layers, some confidence head layers
   - Pattern: `input_shard @ weight → output_shard`

3. **SequenceParallel**: Split along sequence dimension
   - Used for: Layer normalization, some attention operations
   - Pattern: Distribute sequence tokens across devices

### Data Flow

```python
# Example: Intermediate linear layer with ColwiseParallel
input_tensor: [batch, seq_len, hidden_dim]  # Replicated
weight_shard: [hidden_dim, output_dim // tp_size]  # Sharded
output_shard: [batch, seq_len, output_dim // tp_size]  # Sharded

# All-gather to get full output
output_full: [batch, seq_len, output_dim]  # Replicated
```

## Testing

### Unit Tests

```bash
# Run comprehensive test suite
python test_tensor_parallel.py

# Test specific components
python -m unittest test_tensor_parallel.TestTensorParallelConfig
python -m unittest test_tensor_parallel.TestTensorParallelizer
```

### Integration Tests

```bash
# Test different parallelization modes
python example_tensor_parallel_usage.py --mode single
python example_tensor_parallel_usage.py --mode tensor_parallel
python example_tensor_parallel_usage.py --mode hybrid

# Multi-GPU testing
torchrun --nproc_per_node=2 example_tensor_parallel_usage.py --mode tensor_parallel
torchrun --nproc_per_node=4 example_tensor_parallel_usage.py --mode hybrid
```

## Compatibility

### Requirements

- PyTorch >= 2.0 with distributed support
- CUDA-capable GPUs for multi-GPU setups
- NCCL backend for optimal communication

### Backward Compatibility

- **Existing Code**: Fully backward compatible when tensor parallelism is disabled
- **Single GPU**: Automatically falls back to regular execution
- **Data Parallelism**: Works seamlessly with existing data parallelism implementation

### Integration with Existing Features

- **Hybrid Data Parallelism**: Maintains existing pairformer broadcast + diffusion parallel sampling
- **Activation Checkpointing**: Compatible with tensor parallelism
- **Mixed Precision**: Supports float16/bfloat16 training
- **Compilation**: Works with `torch.compile`

## Troubleshooting

### Common Issues

1. **Memory Errors**: Reduce TP size or use conservative strategy
2. **Communication Timeouts**: Check NCCL installation and network connectivity
3. **Numerical Differences**: Verify random seed consistency across ranks

### Debug Commands

```bash
# Check tensor parallelism application
TORCH_LOGS="+dynamo,+distributed" python your_script.py

# Monitor memory usage
nvidia-smi -l 1

# Verify distributed setup
python -c "import torch; print(torch.distributed.is_available())"
```

### Performance Tuning

1. **Optimal TP Size**: Usually 2-4 for best performance/memory trade-off
2. **Mixed Strategies**: Use different TP strategies for different modules
3. **Communication Overlap**: Enable async TP for better overlap
4. **Memory Management**: Use activation checkpointing for large models

## Future Enhancements

1. **Pipeline Parallelism**: Add pipeline parallelism for even larger models
2. **Dynamic Load Balancing**: Adaptive TP size based on workload
3. **Optimized Communication**: Custom kernels for specific operations
4. **Auto-tuning**: Automatic selection of optimal parallelization strategy

## Contributing

When contributing to tensor parallelism:

1. **Test Thoroughly**: Run both unit and integration tests
2. **Maintain Compatibility**: Ensure backward compatibility
3. **Document Changes**: Update this README and code comments
4. **Performance Validation**: Verify performance improvements

## References

- [PyTorch Distributed Tensor Parallelism](https://pytorch.org/docs/stable/distributed.tensor.parallel.html)
- [Megatron-LM Tensor Parallelism](https://arxiv.org/abs/1909.08053)
- [AlphaFold3 Architecture](https://www.nature.com/articles/s41586-024-07487-w)
