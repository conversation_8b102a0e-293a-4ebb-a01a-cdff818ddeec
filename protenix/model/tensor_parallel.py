# Copyright 2024 ByteDance and/or its affiliates.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Tensor Parallelism Implementation for Protenix Model

This module implements tensor parallelism for intermediate modules between
pairformer and diffusion in the Protenix model, following patterns from
parallelize_llama.py.
"""

import torch
import torch.nn as nn
from torch.distributed.device_mesh import DeviceMesh
from torch.distributed.tensor import Replicate, Shard
from torch.distributed.tensor.parallel import (
    ColwiseParallel,
    parallelize_module,
    PrepareModuleInput,
    RowwiseParallel,
    SequenceParallel,
)
from typing import Dict, Optional

from .tensor_parallel_config import TensorParallelConfig, TensorParallelPlanBuilder
from protenix.utils.logger import get_logger

logger = get_logger(__name__)


class ProtenixTensorParallelizer:
    """Main class for applying tensor parallelism to Protenix model components."""
    
    def __init__(self, config: TensorParallelConfig):
        self.config = config
        self.plan_builder = TensorParallelPlanBuilder(config)
        
    def apply_tensor_parallel(
        self,
        model: nn.Module,
        tp_mesh: DeviceMesh,
        enable_float8_tensorwise_tp: bool = False,
        enable_async_tp: bool = False
    ):
        """Apply tensor parallelism to the Protenix model.
        
        Args:
            model: The Protenix model instance
            tp_mesh: Device mesh for tensor parallelism
            enable_float8_tensorwise_tp: Whether to use float8 tensor parallelism
            enable_async_tp: Whether to use async tensor parallelism
        """
        if not self.config.enable_tensor_parallel:
            logger.info("Tensor parallelism is disabled")
            return
        
        logger.info(f"Applying tensor parallelism with tp_size={self.config.tp_size}")
        
        # Get parallelization strategies
        if enable_float8_tensorwise_tp:
            try:
                from torchao.float8.float8_tensor_parallel import (
                    Float8ColwiseParallel,
                    Float8RowwiseParallel,
                    PrepareFloat8ModuleInput,
                )
                rowwise_parallel, colwise_parallel, prepare_module_input = (
                    Float8RowwiseParallel,
                    Float8ColwiseParallel,
                    PrepareFloat8ModuleInput,
                )
                logger.info("Using Float8 tensor parallelism")
            except ImportError:
                logger.warning("Float8 tensor parallelism not available, falling back to standard TP")
                rowwise_parallel, colwise_parallel, prepare_module_input = (
                    RowwiseParallel,
                    ColwiseParallel,
                    PrepareModuleInput,
                )
        else:
            rowwise_parallel, colwise_parallel, prepare_module_input = (
                RowwiseParallel,
                ColwiseParallel,
                PrepareModuleInput,
            )
        
        # Apply tensor parallelism to different components
        self._parallelize_intermediate_linears(model, tp_mesh, colwise_parallel, rowwise_parallel)
        self._parallelize_distogram_head(model, tp_mesh, colwise_parallel)
        self._parallelize_confidence_head(model, tp_mesh, colwise_parallel, rowwise_parallel, prepare_module_input)
        
        # Enable async TP if requested
        if enable_async_tp:
            self._enable_async_tp(tp_mesh)
        
        logger.info("Tensor parallelism applied successfully")
    
    def _parallelize_intermediate_linears(
        self,
        model: nn.Module,
        tp_mesh: DeviceMesh,
        colwise_parallel,
        rowwise_parallel
    ):
        """Apply tensor parallelism to intermediate linear layers."""
        if not self.config.parallelize_intermediate_linears:
            return

        logger.info("Applying tensor parallelism to intermediate linear layers")

        # Build parallelization plan for intermediate linears
        linear_plan = {}

        # Define intermediate linear layers with detailed configurations
        # These layers are critical bottlenecks between pairformer and diffusion
        linear_layer_configs = {
            "linear_no_bias_sinit": {
                "strategy": "colwise",
                "input_layouts": Replicate(),  # Input: s_inputs [..., N_tokens, c_s_inputs=449]
                "output_layouts": Shard(1),    # Output: [..., N_tokens, c_s=384] - shard for pairformer
                "description": "Initial single embedding projection from input features",
                "dimensions": "449 -> 384"
            },
            "linear_no_bias_zinit1": {
                "strategy": "colwise",
                "input_layouts": Shard(1),     # Input: s_init [..., N_tokens, c_s=384] - from sinit
                "output_layouts": Replicate(), # Output: [..., N_tokens, c_z=128] - replicate for pair init
                "description": "First pair embedding initialization from single embedding",
                "dimensions": "384 -> 128"
            },
            "linear_no_bias_zinit2": {
                "strategy": "colwise",
                "input_layouts": Shard(1),     # Input: s_init [..., N_tokens, c_s=384] - from sinit
                "output_layouts": Replicate(), # Output: [..., N_tokens, c_z=128] - replicate for pair init
                "description": "Second pair embedding initialization from single embedding",
                "dimensions": "384 -> 128"
            },
            "linear_no_bias_token_bond": {
                "strategy": "colwise",
                "input_layouts": Replicate(),  # Input: token_bonds [..., N_tokens, N_tokens, 1]
                "output_layouts": Replicate(), # Output: [..., N_tokens, N_tokens, c_z=128]
                "description": "Token bond embedding projection",
                "dimensions": "1 -> 128"
            },
            "linear_no_bias_z_cycle": {
                "strategy": "colwise",
                "input_layouts": Replicate(),  # Input: z [..., N_tokens, N_tokens, c_z=128]
                "output_layouts": Replicate(), # Output: [..., N_tokens, N_tokens, c_z=128]
                "description": "Pair embedding recycling projection",
                "dimensions": "128 -> 128"
            },
            "linear_no_bias_s": {
                "strategy": "colwise",
                "input_layouts": Replicate(),  # Input: s [..., N_tokens, c_s=384]
                "output_layouts": Shard(1),    # Output: [..., N_tokens, c_s=384] - shard for pairformer
                "description": "Single embedding recycling projection",
                "dimensions": "384 -> 384"
            }
        }

        for layer_name, config in linear_layer_configs.items():
            if hasattr(model, layer_name):
                if config["strategy"] == "colwise":
                    linear_plan[layer_name] = colwise_parallel(
                        input_layouts=config["input_layouts"],
                        output_layouts=config["output_layouts"],
                        use_local_output=False
                    )
                elif config["strategy"] == "rowwise":
                    linear_plan[layer_name] = rowwise_parallel(
                        input_layouts=config["input_layouts"],
                        output_layouts=config["output_layouts"]
                    )

                logger.debug(f"Added TP plan for {layer_name}: {config['description']} ({config['dimensions']})")

        if linear_plan:
            try:
                parallelize_module(
                    module=model,
                    device_mesh=tp_mesh,
                    parallelize_plan=linear_plan
                )
                logger.info(f"Applied TP to {len(linear_plan)} intermediate linear layers")
            except Exception as e:
                logger.warning(f"Failed to apply TP to intermediate linear layers: {e}")
                # Continue without TP for these modules
    
    def _parallelize_distogram_head(
        self,
        model: nn.Module,
        tp_mesh: DeviceMesh,
        colwise_parallel
    ):
        """Apply tensor parallelism to distogram head."""
        if not self.config.parallelize_distogram_head or not hasattr(model, 'distogram_head'):
            return

        logger.info("Applying tensor parallelism to distogram head")

        # Distogram head has a single linear layer that outputs distance bins
        # Use ColwiseParallel to split the output dimension (no_bins) across devices
        # Input: [..., N_token, N_token, c_z=128]
        # Output: [..., N_token, N_token, no_bins=64]
        distogram_plan = {
            "linear": colwise_parallel(
                input_layouts=Replicate(),  # Input z is replicated across all TP ranks
                output_layouts=Replicate(), # Output needs to be replicated for subsequent use
                use_local_output=False      # Gather outputs from all ranks
            )
        }

        try:
            parallelize_module(
                module=model.distogram_head,
                device_mesh=tp_mesh,
                parallelize_plan=distogram_plan
            )
            logger.info("Applied TP to distogram head linear layer")
        except Exception as e:
            logger.warning(f"Failed to apply TP to distogram head: {e}")
            # Continue without TP for this module
    
    def _parallelize_confidence_head(
        self,
        model: nn.Module,
        tp_mesh: DeviceMesh,
        colwise_parallel,
        rowwise_parallel,
        prepare_module_input
    ):
        """Apply tensor parallelism to confidence head."""
        if not self.config.parallelize_confidence_head or not hasattr(model, 'confidence_head'):
            return

        logger.info("Applying tensor parallelism to confidence head")

        confidence_plan = {}

        # Parallelize linear layers in confidence head
        if self.config.confidence_head_tp_strategy in ["full", "linear_only"]:
            # Define linear layers with their specific parallelization strategies
            linear_layer_configs = {
                "linear_no_bias_s1": {
                    "strategy": "colwise",
                    "input_layouts": Replicate(),  # Input: s_inputs [..., N_tokens, c_s_inputs=449]
                    "output_layouts": Replicate(), # Output: [..., N_tokens, c_z=128]
                    "description": "Single input embedding to pair embedding projection"
                },
                "linear_no_bias_s2": {
                    "strategy": "colwise",
                    "input_layouts": Replicate(),  # Input: s_inputs [..., N_tokens, c_s_inputs=449]
                    "output_layouts": Replicate(), # Output: [..., N_tokens, c_z=128]
                    "description": "Second single input embedding projection"
                },
                "linear_no_bias_d": {
                    "strategy": "colwise",
                    "input_layouts": Replicate(),  # Input: distance one-hot [..., N_tokens, N_tokens, num_bins]
                    "output_layouts": Replicate(), # Output: [..., N_tokens, N_tokens, c_z=128]
                    "description": "Distance embedding projection"
                },
                "linear_no_bias_pae": {
                    "strategy": "colwise",
                    "input_layouts": Replicate(),  # Input: z_pair [..., N_tokens, N_tokens, c_z=128]
                    "output_layouts": Replicate(), # Output: [..., N_tokens, N_tokens, b_pae=64]
                    "description": "PAE (Predicted Aligned Error) head"
                },
                "linear_no_bias_pde": {
                    "strategy": "colwise",
                    "input_layouts": Replicate(),  # Input: z_pair [..., N_tokens, N_tokens, c_z=128]
                    "output_layouts": Replicate(), # Output: [..., N_tokens, N_tokens, b_pde=64]
                    "description": "PDE (Predicted Distance Error) head"
                }
            }

            for layer_name, config in linear_layer_configs.items():
                if hasattr(model.confidence_head, layer_name):
                    confidence_plan[layer_name] = colwise_parallel(
                        input_layouts=config["input_layouts"],
                        output_layouts=config["output_layouts"],
                        use_local_output=False
                    )
                    logger.debug(f"Added TP plan for {layer_name}: {config['description']}")

        # Parallelize pairformer stack in confidence head
        if (self.config.parallelize_confidence_pairformer and
            self.config.confidence_head_tp_strategy in ["full", "pairformer_only"]):

            # Apply tensor parallelism to the pairformer stack within confidence head
            self._parallelize_confidence_pairformer_stack(
                model.confidence_head, tp_mesh, colwise_parallel, rowwise_parallel, prepare_module_input
            )

        if confidence_plan:
            try:
                parallelize_module(
                    module=model.confidence_head,
                    device_mesh=tp_mesh,
                    parallelize_plan=confidence_plan
                )
                logger.info(f"Applied TP to confidence head with {len(confidence_plan)} layers")
            except Exception as e:
                logger.warning(f"Failed to apply TP to confidence head: {e}")
                # Continue without TP for this module
    
    def _parallelize_confidence_pairformer_stack(
        self,
        confidence_head: nn.Module,
        tp_mesh: DeviceMesh,
        colwise_parallel,
        rowwise_parallel,
        prepare_module_input
    ):
        """Apply tensor parallelism to pairformer stack within confidence head."""
        if not hasattr(confidence_head, 'pairformer_stack'):
            logger.debug("Confidence head does not have pairformer_stack")
            return

        logger.info("Applying tensor parallelism to confidence head pairformer stack")

        # Apply similar strategy as main pairformer but adapted for confidence head
        pairformer_stack = confidence_head.pairformer_stack

        # Check if pairformer_stack has blocks attribute
        if hasattr(pairformer_stack, 'blocks'):
            blocks = pairformer_stack.blocks
        elif hasattr(pairformer_stack, 'layers'):
            blocks = pairformer_stack.layers
        else:
            logger.warning("Could not find blocks or layers in confidence head pairformer_stack")
            return

        # Apply tensor parallelism to each pairformer block
        blocks_parallelized = 0
        for block_idx, pairformer_block in enumerate(blocks):
            try:
                block_plan = self._build_pairformer_block_plan(
                    colwise_parallel, rowwise_parallel, prepare_module_input
                )

                if block_plan:  # Only apply if we have a valid plan
                    parallelize_module(
                        module=pairformer_block,
                        device_mesh=tp_mesh,
                        parallelize_plan=block_plan
                    )
                    blocks_parallelized += 1
                    logger.debug(f"Applied TP to confidence pairformer block {block_idx}")

            except Exception as e:
                logger.warning(f"Failed to apply TP to confidence pairformer block {block_idx}: {e}")
                continue

        logger.info(f"Applied TP to {blocks_parallelized}/{len(blocks)} pairformer blocks in confidence head")
    
    def _build_pairformer_block_plan(
        self,
        colwise_parallel,
        rowwise_parallel,
        prepare_module_input
    ) -> Dict[str, any]:
        """Build tensor parallelism plan for a pairformer block."""
        # This follows the pattern from parallelize_llama.py but adapted for pairformer blocks
        plan = {}

        # Add sequence parallel for layer norms if enabled
        if self.config.use_sequence_parallel:
            # Apply sequence parallelism to normalization layers
            norm_layers = [
                "pair_transition.layernorm1",
                "single_transition.layernorm1",
                "triangle_attention_starting_node.layernorm",
                "triangle_attention_ending_node.layernorm",
                "triangle_multiplication_outgoing.layernorm",
                "triangle_multiplication_incoming.layernorm"
            ]

            for norm_layer in norm_layers:
                plan[norm_layer] = SequenceParallel()

        # Parallelize triangle attention layers
        triangle_attention_layers = [
            "triangle_attention_starting_node",
            "triangle_attention_ending_node"
        ]

        for layer_name in triangle_attention_layers:
            # Attention projections
            plan[f"{layer_name}.linear_q"] = colwise_parallel(
                input_layouts=Replicate(),
                output_layouts=Shard(-1)
            )
            plan[f"{layer_name}.linear_k"] = colwise_parallel(
                input_layouts=Replicate(),
                output_layouts=Shard(-1)
            )
            plan[f"{layer_name}.linear_v"] = colwise_parallel(
                input_layouts=Replicate(),
                output_layouts=Shard(-1)
            )
            plan[f"{layer_name}.linear_o"] = rowwise_parallel(
                input_layouts=Shard(-1),
                output_layouts=Replicate()
            )

        # Parallelize triangle multiplication layers
        triangle_mult_layers = [
            "triangle_multiplication_outgoing",
            "triangle_multiplication_incoming"
        ]

        for layer_name in triangle_mult_layers:
            # Linear projections in triangle multiplication
            plan[f"{layer_name}.linear_a_p"] = colwise_parallel(
                input_layouts=Replicate(),
                output_layouts=Shard(-1)
            )
            plan[f"{layer_name}.linear_a_g"] = colwise_parallel(
                input_layouts=Replicate(),
                output_layouts=Shard(-1)
            )
            plan[f"{layer_name}.linear_b_p"] = colwise_parallel(
                input_layouts=Replicate(),
                output_layouts=Shard(-1)
            )
            plan[f"{layer_name}.linear_b_g"] = colwise_parallel(
                input_layouts=Replicate(),
                output_layouts=Shard(-1)
            )
            plan[f"{layer_name}.linear_g"] = rowwise_parallel(
                input_layouts=Shard(-1),
                output_layouts=Replicate()
            )
            plan[f"{layer_name}.linear_z"] = rowwise_parallel(
                input_layouts=Shard(-1),
                output_layouts=Replicate()
            )

        # Parallelize transition layers
        transition_layers = ["pair_transition", "single_transition"]

        for layer_name in transition_layers:
            # Transition feed-forward networks
            plan[f"{layer_name}.linear_no_bias_a"] = colwise_parallel(
                input_layouts=Replicate(),
                output_layouts=Shard(-1)
            )
            plan[f"{layer_name}.linear_no_bias_b"] = colwise_parallel(
                input_layouts=Replicate(),
                output_layouts=Shard(-1)
            )
            plan[f"{layer_name}.linear_no_bias"] = rowwise_parallel(
                input_layouts=Shard(-1),
                output_layouts=Replicate()
            )

        logger.debug(f"Built pairformer block plan with {len(plan)} components")
        return plan
    
    def _enable_async_tp(self, tp_mesh: DeviceMesh):
        """Enable asynchronous tensor parallelism."""
        try:
            from torch.distributed._symmetric_memory import enable_symm_mem_for_group
            torch._inductor.config._micro_pipeline_tp = True
            enable_symm_mem_for_group(tp_mesh.get_group().group_name)
            logger.info("Enabled async tensor parallelism")
        except Exception as e:
            logger.warning(f"Failed to enable async TP: {e}")


def apply_protenix_tensor_parallel(
    model: nn.Module,
    tp_mesh: DeviceMesh,
    config: Optional[TensorParallelConfig] = None,
    enable_float8_tensorwise_tp: bool = False,
    enable_async_tp: bool = False
):
    """Apply tensor parallelism to Protenix model.
    
    Args:
        model: Protenix model instance
        tp_mesh: Device mesh for tensor parallelism
        config: Tensor parallelism configuration
        enable_float8_tensorwise_tp: Whether to use float8 TP
        enable_async_tp: Whether to use async TP
    """
    if config is None:
        config = TensorParallelConfig(
            enable_tensor_parallel=True,
            tp_size=tp_mesh.size()
        )
    
    parallelizer = ProtenixTensorParallelizer(config)
    parallelizer.apply_tensor_parallel(
        model=model,
        tp_mesh=tp_mesh,
        enable_float8_tensorwise_tp=enable_float8_tensorwise_tp,
        enable_async_tp=enable_async_tp
    )
