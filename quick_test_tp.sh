#!/bin/bash
# Quick test for tensor parallelism integration

PROJECT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
echo "Project directory: ${PROJECT_DIR}"

export CUDA_VISIBLE_DEVICES=0,1
export LAYERNORM_TYPE=fast_layernorm
export USE_DEEPSPEED_EVO_ATTTENTION=true

# Model checkpoint path
load_checkpoint_path="/jfs/yakun-li/yakun_li_genbio_ai/data/protenix/release_model/model_v0.2.0.pt"

# Inference parameters
N_sample=1  # Reduced for quick test
N_step=50   # Reduced for quick test
N_cycle=3   # Reduced for quick test
seed=101
input_json_path=${PROJECT_DIR}/examples/example.json

# Create timestamp for unique output directories
timestamp=$(date +"%Y%m%d_%H%M%S")

echo "Starting quick tensor parallelism test..."
echo "Timestamp: ${timestamp}"

# Test: Data Parallelism + Tensor Parallelism (Quick)
echo ""
echo "=================================================="
echo "Quick Test: Data Parallelism + Tensor Parallelism"
echo "=================================================="

dump_dir_hybrid="./outputs/quick_test_dp_tp_${timestamp}"
echo "DP+TP results will be saved to: ${dump_dir_hybrid}"

torchrun --nproc_per_node=2 ${PROJECT_DIR}/runner/inference_profiler.py \
--load_checkpoint_path ${load_checkpoint_path} \
--seeds ${seed} \
--dump_dir ${dump_dir_hybrid} \
--input_json_path ${input_json_path} \
--model.N_cycle ${N_cycle} \
--sample_diffusion.N_sample ${N_sample} \
--sample_diffusion.N_step ${N_step} \
--enable_tensor_parallel \
--tp_size=2 \
--tp_strategy=balanced

echo "Quick DP+TP test completed."

# Check results
echo ""
echo "=================================="
echo "Quick Results Check"
echo "=================================="

# Check if timing files exist
hybrid_timing="${dump_dir_hybrid}/timing/timing_summary.json"

if [ -f "$hybrid_timing" ]; then
    echo "✅ Timing file found: $hybrid_timing"
    
    # Extract timing information
    python3 -c "
import json
try:
    with open('$hybrid_timing', 'r') as f:
        data = json.load(f)
    samples = data.get('samples', [])
    if samples:
        sample = samples[0]
        timing = sample['timing']
        total_time = timing.get('total_inference', 0)
        model_time = timing.get('model_forward_total', 0)
        print(f'Total inference time: {total_time:.3f}s')
        print(f'Model forward time: {model_time:.3f}s')
        
        # Check for TP indicators
        tp_components = ['input_embedder', 'pairformer_stack', 'confidence_head', 'distogram_head', 'diffusion_module']
        print('\\nComponent timing:')
        for comp in tp_components:
            if comp in timing:
                print(f'  {comp}: {timing[comp]:.3f}s')
    else:
        print('No samples found in timing data')
except Exception as e:
    print(f'Error reading timing data: {e}')
"
else
    echo "❌ Timing file not found: $hybrid_timing"
fi

# Check logs for tensor parallelism messages
echo ""
echo "Checking for Tensor Parallelism Messages:"
echo "=========================================="

log_files=$(find ${dump_dir_hybrid} -name "*.log" -o -name "*.out" 2>/dev/null)
if [ -n "$log_files" ]; then
    echo "Found log files, checking for TP messages..."
    
    if echo "$log_files" | xargs grep -l "tensor parallelism" 2>/dev/null; then
        echo "✅ Found tensor parallelism related messages"
        echo "Key messages:"
        echo "$log_files" | xargs grep -i "tensor parallelism" 2>/dev/null | head -5
    else
        echo "❌ No tensor parallelism messages found in logs"
    fi
else
    echo "No log files found to check"
fi

# Check stdout/stderr for TP messages
echo ""
echo "Checking recent output for TP messages..."
if grep -i "tensor parallelism" /tmp/torchelastic_*/none_*/attempt_*/*/error.json 2>/dev/null; then
    echo "✅ Found TP messages in error logs"
else
    echo "No TP messages in recent error logs"
fi

echo ""
echo "Quick test summary:"
echo "=================="
echo "Output directory: $dump_dir_hybrid"
echo ""
echo "Next steps:"
echo "1. Check if tensor parallelism was applied successfully"
echo "2. Compare with baseline DP-only performance"
echo "3. If TP is working, run full performance comparison"

echo ""
echo "Quick test completed!"
