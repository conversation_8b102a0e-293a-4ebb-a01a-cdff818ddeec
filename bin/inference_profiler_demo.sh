#!/bin/bash
# Copyright 2024 ByteDance and/or its affiliates.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

PROJECT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)
echo ${PROJECT_DIR}

# Check if GPU ID is provided
if [ -z "$1" ]; then
  echo "Error: GPU ID not provided"
  echo "Usage: $0 <GPU_ID>"
  exit 1
fi

export CUDA_VISIBLE_DEVICES=$1
export LAYERNORM_TYPE=fast_layernorm
export USE_DEEPSPEED_EVO_ATTTENTION=true

# Model checkpoint path
load_checkpoint_path="/jfs/yakun-li/yakun_li_genbio_ai/data/protenix/release_model/model_v0.2.0.pt"

# Inference parameters
N_sample=5
N_step=200
N_cycle=10
seed=101
use_deepspeed_evo_attention=true
input_json_path=${PROJECT_DIR}/examples/example.json

# Output directory with timestamp for profiling results
timestamp=$(date +"%Y%m%d_%H%M%S")
dump_dir="./outputs/output_profiler_${timestamp}"

echo "Starting inference profiling..."
echo "Results will be saved to: ${dump_dir}"
echo "Timing information will be in: ${dump_dir}/timing/"

# Run the profiler script
# python3 ${PROJECT_DIR}/runner/inference_profiler.py \
# --load_checkpoint_path ${load_checkpoint_path} \
# --seeds ${seed} \
# --dump_dir ${dump_dir} \
# --input_json_path ${input_json_path} \
# --model.N_cycle ${N_cycle} \
# --sample_diffusion.N_sample ${N_sample} \
# --sample_diffusion.N_step ${N_step}

torchrun --nproc_per_node=2 ${PROJECT_DIR}/runner/inference_profiler.py \
--load_checkpoint_path ${load_checkpoint_path} \
--seeds ${seed} \
--dump_dir ${dump_dir} \
--input_json_path ${input_json_path} \
--model.N_cycle ${N_cycle} \
--sample_diffusion.N_sample ${N_sample} \
--sample_diffusion.N_step ${N_step}
--enable_tensor_parallel \
--tp_size=2 \

echo "Inference profiling completed."
echo "Check timing results in: ${dump_dir}/timing/timing_summary.json"