#!/bin/bash
# Copyright 2024 ByteDance and/or its affiliates.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Test script to verify tensor parallelism integration and performance

PROJECT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
echo "Project directory: ${PROJECT_DIR}"

# Check if GPU ID is provided
if [ -z "$1" ]; then
  echo "Error: GPU ID not provided"
  echo "Usage: $0 <GPU_ID>"
  exit 1
fi

export CUDA_VISIBLE_DEVICES=$1
export LAYERNORM_TYPE=fast_layernorm
export USE_DEEPSPEED_EVO_ATTTENTION=true

# Model checkpoint path
load_checkpoint_path="/jfs/yakun-li/yakun_li_genbio_ai/data/protenix/release_model/model_v0.2.0.pt"

# Inference parameters
N_sample=5
N_step=200
N_cycle=10
seed=101
input_json_path=${PROJECT_DIR}/examples/example.json

# Create timestamp for unique output directories
timestamp=$(date +"%Y%m%d_%H%M%S")

echo "Starting tensor parallelism integration test..."
echo "Timestamp: ${timestamp}"

# Test 1: Data Parallelism Only (Baseline)
echo ""
echo "=========================================="
echo "Test 1: Data Parallelism Only (Baseline)"
echo "=========================================="

dump_dir_dp="./outputs/test_dp_only_${timestamp}"
echo "DP-only results will be saved to: ${dump_dir_dp}"

torchrun --nproc_per_node=2 ${PROJECT_DIR}/runner/inference_profiler.py \
--load_checkpoint_path ${load_checkpoint_path} \
--seeds ${seed} \
--dump_dir ${dump_dir_dp} \
--input_json_path ${input_json_path} \
--model.N_cycle ${N_cycle} \
--sample_diffusion.N_sample ${N_sample} \
--sample_diffusion.N_step ${N_step}

echo "DP-only test completed."

# Test 2: Data Parallelism + Tensor Parallelism
echo ""
echo "=================================================="
echo "Test 2: Data Parallelism + Tensor Parallelism"
echo "=================================================="

dump_dir_hybrid="./outputs/test_dp_tp_${timestamp}"
echo "DP+TP results will be saved to: ${dump_dir_hybrid}"

torchrun --nproc_per_node=2 ${PROJECT_DIR}/runner/inference_profiler.py \
--load_checkpoint_path ${load_checkpoint_path} \
--seeds ${seed} \
--dump_dir ${dump_dir_hybrid} \
--input_json_path ${input_json_path} \
--model.N_cycle ${N_cycle} \
--sample_diffusion.N_sample ${N_sample} \
--sample_diffusion.N_step ${N_step} \
--enable_tensor_parallel \
--tp_size=2 \
--tp_strategy=balanced

echo "DP+TP test completed."

# Test 3: Tensor Parallelism Only
echo ""
echo "=================================="
echo "Test 3: Tensor Parallelism Only"
echo "=================================="

dump_dir_tp="./outputs/test_tp_only_${timestamp}"
echo "TP-only results will be saved to: ${dump_dir_tp}"

# Use single process with internal TP
python3 ${PROJECT_DIR}/runner/inference_profiler.py \
--load_checkpoint_path ${load_checkpoint_path} \
--seeds ${seed} \
--dump_dir ${dump_dir_tp} \
--input_json_path ${input_json_path} \
--model.N_cycle ${N_cycle} \
--sample_diffusion.N_sample ${N_sample} \
--sample_diffusion.N_step ${N_step} \
--enable_tensor_parallel \
--tp_size=2 \
--tp_strategy=balanced

echo "TP-only test completed."

# Performance Analysis
echo ""
echo "=================================="
echo "Performance Analysis"
echo "=================================="

# Check if timing files exist
dp_timing="${dump_dir_dp}/timing/timing_summary.json"
hybrid_timing="${dump_dir_hybrid}/timing/timing_summary.json"
tp_timing="${dump_dir_tp}/timing/timing_summary.json"

if [ -f "$dp_timing" ] && [ -f "$hybrid_timing" ]; then
    echo "Analyzing DP vs DP+TP performance..."
    python3 ${PROJECT_DIR}/diagnose_tensor_parallel_performance.py \
        --timing_dir ${dump_dir_hybrid}/timing \
        --compare_with ${dump_dir_dp}/timing \
        --output_report ${dump_dir_hybrid}/timing/performance_analysis.txt
else
    echo "Warning: Timing files not found for comparison"
    echo "DP timing: $dp_timing"
    echo "Hybrid timing: $hybrid_timing"
fi

# Quick timing comparison
echo ""
echo "Quick Timing Summary:"
echo "====================="

extract_total_time() {
    local timing_file=$1
    if [ -f "$timing_file" ]; then
        python3 -c "
import json
try:
    with open('$timing_file', 'r') as f:
        data = json.load(f)
    samples = data.get('samples', [])
    if samples:
        total_time = samples[0]['timing'].get('total_inference', 0)
        print(f'{total_time:.3f}s')
    else:
        print('N/A')
except:
    print('Error')
"
    else
        echo "N/A"
    fi
}

dp_time=$(extract_total_time "$dp_timing")
hybrid_time=$(extract_total_time "$hybrid_timing")
tp_time=$(extract_total_time "$tp_timing")

echo "DP-only time:    $dp_time"
echo "DP+TP time:      $hybrid_time"
echo "TP-only time:    $tp_time"

# Calculate speedup if possible
if [ "$dp_time" != "N/A" ] && [ "$hybrid_time" != "N/A" ]; then
    speedup=$(python3 -c "
try:
    dp = float('$dp_time'.replace('s', ''))
    hybrid = float('$hybrid_time'.replace('s', ''))
    if hybrid > 0:
        speedup = dp / hybrid
        improvement = (dp - hybrid) / dp * 100
        print(f'Speedup: {speedup:.2f}x, Improvement: {improvement:+.1f}%')
    else:
        print('Cannot calculate speedup')
except:
    print('Error calculating speedup')
")
    echo "DP vs DP+TP: $speedup"
fi

# Check logs for tensor parallelism application
echo ""
echo "Checking for Tensor Parallelism Application:"
echo "============================================="

check_tp_logs() {
    local log_dir=$1
    local test_name=$2
    
    echo "Checking $test_name logs..."
    
    # Look for TP application messages in any log files
    if find "$log_dir" -name "*.log" -o -name "*.out" 2>/dev/null | xargs grep -l "Tensor parallelism applied successfully" 2>/dev/null; then
        echo "✅ Tensor parallelism was applied in $test_name"
    else
        echo "❌ No evidence of tensor parallelism application in $test_name"
    fi
    
    # Check for TP-related error messages
    if find "$log_dir" -name "*.log" -o -name "*.out" 2>/dev/null | xargs grep -l "Failed to apply tensor parallelism" 2>/dev/null; then
        echo "⚠️  Tensor parallelism application failed in $test_name"
    fi
}

check_tp_logs "$dump_dir_hybrid" "DP+TP"
check_tp_logs "$dump_dir_tp" "TP-only"

echo ""
echo "Test Summary:"
echo "============="
echo "DP-only output:  $dump_dir_dp"
echo "DP+TP output:    $dump_dir_hybrid"
echo "TP-only output:  $dump_dir_tp"
echo ""
echo "Next steps:"
echo "1. Check the performance analysis report in: ${dump_dir_hybrid}/timing/performance_analysis.txt"
echo "2. Review logs for tensor parallelism application messages"
echo "3. If TP is not being applied, check import errors and configuration"
echo "4. Consider testing with larger models or different tp_size values"

echo ""
echo "Integration test completed!"
